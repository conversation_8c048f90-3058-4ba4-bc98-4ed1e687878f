import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:zrreport/common/index.dart';
import 'models/match_detail_models.dart';
import 'provider/match_detail_provider.dart';
import 'enterprise_info/enterprise_info_widget.dart';
import 'tax_info/tax_info_widget.dart';
import 'invoice_info/invoice_info_widget.dart';
import 'match_result/match_result_widget.dart';

/// 匹配详情页面
class MatchDetailPage extends ConsumerStatefulWidget {
  final MatchDetailParams params;

  const MatchDetailPage({
    super.key,
    required this.params,
  });

  @override
  ConsumerState<MatchDetailPage> createState() => _MatchDetailPageState();
}

class _MatchDetailPageState extends ConsumerState<MatchDetailPage> {
  @override
  void initState() {
    super.initState();
    // 页面初始化时加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(matchDetailNotifierProvider.notifier).initialize(widget.params);
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(matchDetailNotifierProvider);
    final notifier = ref.read(matchDetailNotifierProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('匹配结果'),
        elevation: 0,
      ),
      backgroundColor: const Color(0xFFF5F5F5),
      body: Column(
        children: [
          // 主要内容区域（可滚动）
          Expanded(
            child: state.isLoading
                ? const LoadingWidget()
                : state.errorMessage != null
                    ? ErrorStatusWidget(
                        text: state.errorMessage!,
                        onAttempt: () => notifier.reload(widget.params),
                      )
                    : state.summaryData == null
                        ? const EmptyWidget(text: '暂无数据')
                        : _buildContent(state, notifier),
          ),

          // 分享按钮（固定在底部）
          if (state.summaryData != null) _buildShareButton(),
        ],
      ),
    );
  }

  Widget _buildContent(MatchDetailState state, MatchDetailNotifier notifier) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // 提示信息
          Container(
            padding: const EdgeInsets.only(left: 16, right: 16, top: 12),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: AppColors.textColor9,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    '该匹配产品仅做推荐参考，请结合实际情况选择适合产品',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.textColor9,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 8),

          // 蓝色卡片
          _buildSummaryCard(state.summaryData!),

          const SizedBox(height: 16),

          // 企业分析详情标题
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: Center(
              child: Text(
                '企业分析详情',
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.textColor3,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),

          // 标签页按钮
          _buildTabButtons(state, notifier),

          const SizedBox(height: 16),

          // 标签页内容
          _buildTabContent(state),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(MatchSummaryData data) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF488AFD), Color(0xFF6BA3FF)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      data.enterpriseName,
                      style: const TextStyle(
                        fontSize: 18,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '报告生成时间：${data.reportCreateTime}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.orange,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  '企业',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${data.productNum}',
                      style: const TextStyle(
                        fontSize: 32,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Text(
                      '准入产品',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${data.totalQuota}万',
                      style: const TextStyle(
                        fontSize: 32,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Text(
                      '最高可贷',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
              // 右侧图标装饰
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.account_balance,
                  size: 40,
                  color: Colors.white54,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabButtons(
      MatchDetailState state, MatchDetailNotifier notifier) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: MatchDetailTab.values.asMap().entries.map((entry) {
          final index = entry.key;
          final tab = entry.value;
          final isSelected = state.selectedTabIndex == index;

          return Expanded(
            child: GestureDetector(
              onTap: () => notifier.selectTab(index),
              child: Container(
                height: 40,
                margin: EdgeInsets.only(
                    right: index < MatchDetailTab.values.length - 1 ? 8 : 0),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.primary.withAlpha(33)
                      : AppColors.lightGrey,
                  borderRadius: BorderRadius.circular(16),
                  border: isSelected
                      ? Border.all(
                          color: AppColors.primary,
                          width: 1,
                        )
                      : null,
                ),
                child: Center(
                  child: Text(
                    tab.title,
                    style: TextStyle(
                      fontSize: 14,
                      color:
                          isSelected ? AppColors.primary : AppColors.textColor6,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildTabContent(MatchDetailState state) {
    switch (state.selectedTabIndex) {
      case 0:
        return EnterpriseInfoWidget(enterpriseId: widget.params.id);
      case 1:
        return TaxInfoWidget(
          shareCode: widget.params.id,
        );
      case 2:
        return const InvoiceInfoWidget();
      case 3:
        return MatchResultWidget(
          matchId: widget.params.id,
          matchType: widget.params.type,
          createTime: '2024-01-01 12:00:00',
        );
      default:
        return Container();
    }
  }

  Widget _buildShareButton() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0x1A000000),
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          height: 48,
          child: ElevatedButton(
            onPressed: () {
              // TODO: 实现分享功能
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
            ),
            child: const Text(
              '分享方案',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
