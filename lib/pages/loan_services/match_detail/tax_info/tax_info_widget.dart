import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/report_detail/dynamic_data_source.dart';
import 'package:zrreport/pages/report_detail/widget/section_header.dart';
import 'package:zrreport/pages/report_detail/widget/section_left_more_header.dart';

import 'paid_taxes_provider.dart';
import 'taxes_provider.dart';

class TaxInfoWidget extends BasePage {
  final String shareCode;
  final bool isExample;
  const TaxInfoWidget(
      {super.key, required this.shareCode, this.isExample = false});

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ProviderScope.containerOf(context)
          .read(taxInfoProvider.notifier)
          .getTaxesDetail(shareCode, isExample: isExample);
    });

    return Padding(
      padding: EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          children: [
            Consumer(
              builder: (context, ref, child) {
                final response = ref.watch(taxInfoProvider);
                switch (response.pageStatus) {
                  case PageStatus.initial:
                  case PageStatus.loading:
                    return Center(
                      child: LoadingWidget(),
                    );
                  case PageStatus.success:
                    return _buildContentView();
                  case PageStatus.empty:
                    return Container(
                      child: Text(''),
                    );
                  case PageStatus.error:
                    return Container(
                      child: Text('error'),
                    );
                }
              },
            )
          ],
        ),
      ),
    );
  }

  Widget _buildContentView() {
    return Consumer(builder: (context, ref, child) {
      final response = ref.read(taxInfoProvider);
      return Column(
        children: [
          // header list
          ...response.taxInfoHeadList.map((e) {
            return AdaptiveTextRow(
                label: e.title, value: e.value, isOdd: e.dark);
          }),

          SizedBox(height: 24),

          ///  纳税数据分析
          response.hasEnterpriseMonthPaidList
              ? _buildEnterpriseMonthPaidTable(response)
              : Container(),

          /// 滞纳金情况表
          // response.hasTaxLateFeesList
          //     ? _buildTaxLateFeesListTable(response)
          //     : Container(),

          _buildTableView(
            '滞纳金情况',
            response.taxLateFeesListTableHeadData,
            response.taxLateFeesListTableData(),
          ),

          // /// 税务违法违规信息
          // response.hasLawRegulationViolationVoList
          //     ? // 税务违法违规信息
          //     _buildLawRegulationViolationVoListTable(response, context)
          //     : Container(),

          /// 近两年纳税信用评级表
          _buildTableView(
              '近两年纳税信用评级',
              response.creditEvaluationListTableHeadData,
              response.creditEvaluationListTableData(),
              columnWidthMode: ColumnWidthMode.lastColumnFill),

          // response.hasCreditEvaluationList
          //     ? _buildCreditEvaluationListTable(response)
          //     : Container(),

          /// 财务情况
          response.hasSpiderFinanceInfos
              ? _buildSpiderFinanceInfoTable(response)
              : Container(),

          /// 近三年纳税信息
          _buildLastThreeTaxesTable(),

          /// 欠税信息
          // todo
        ],
      );
    });
  }

  /// 近三年纳税信息
  Widget _buildLastThreeTaxesTable() {
    return Consumer(builder: (context, ref, child) {
      final response = ref.watch(paidTaxInfoProvider);
      if (!response.hasPaidTaxInfo) {
        ref.read(paidTaxInfoProvider.notifier).getPaidTaxesDetail(
              shareCode,
              response.taxType,
            );
      }
      return Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 16,
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(child: SectionHeader(title: '近三年纳税信息')),
                  switchWidget(),
                  SizedBox(width: 15)
                ],
              ),
              SizedBox(height: 12),
              Text(
                response.taxTipContent(),
                style: TextStyle(
                  fontSize: 13,
                  color: Color(0xff999999),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 12),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Color(0xffE5E7EB),
                    width: 1,
                  ),
                ),
                clipBehavior: Clip.antiAlias,
                child: SfDataGrid(
                  source: DynamicDataSource(
                    data: response.padiTaxesListTableData,
                    columns: response.padiTaxesListTableHeadData,
                    cellBuilder: (context, cell, index) {
                      // 在这里处理table body
                      return Container(
                        alignment: Alignment.center,
                        padding: EdgeInsets.symmetric(
                          vertical: 8.0,
                          horizontal: index == 0 ? 8.0 : 0,
                        ),
                        color: Colors.white,
                        child: Text(
                          cell.value ?? '',
                        ),
                      );
                    },
                  ),
                  // frozenColumnsCount: 1, // 第一列固定
                  gridLinesVisibility: GridLinesVisibility.both,
                  headerGridLinesVisibility: GridLinesVisibility.both,
                  showHorizontalScrollbar: false,
                  shrinkWrapRows: true, // 自动撑开高度
                  // shrinkWrapColumns: true,
                  verticalScrollPhysics:
                      NeverScrollableScrollPhysics(), // 关闭滚动，让外层控制
                  columns: response.padiTaxesListTableHeadData
                      .asMap()
                      .entries
                      .map((entry) {
                    // 在这里处理table head
                    final index = entry.key;
                    final e = entry.value;
                    return GridColumn(
                      columnName: e,
                      width: index == 0
                          ? 60
                          : (MediaQuery.of(context).size.width - 60 - 30) /
                              3, // 第一列宽度不同
                      label: Container(
                        color: Color(0xffF1F5F9),
                        padding: EdgeInsets.symmetric(
                            vertical: 16, horizontal: index == 0 ? 8 : 0),
                        alignment: Alignment.center,
                        child: Text(
                          e,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              )
            ],
          ));
    });
  }

  Widget switchWidget() {
    return Consumer(
      builder: (context, ref, child) {
        final response = ref.watch(paidTaxInfoProvider);
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: Color(0xffd3e3fe),
          ),
          height: 24,
          width: 110,
          child: Row(
            children: [
              Expanded(
                child: _buildItemButton(
                  title: '正税',
                  selected: response.taxType == TaxType.normalTax,
                  onPressed: () {
                    if (response.taxType != TaxType.normalTax) {
                      ref
                          .read(paidTaxInfoProvider.notifier)
                          .getPaidTaxesDetail(shareCode, TaxType.normalTax);
                    }
                  },
                ),
              ),
              Expanded(
                child: _buildItemButton(
                  title: '附加税',
                  selected: response.taxType == TaxType.additionalTax,
                  onPressed: () {
                    if (response.taxType != TaxType.additionalTax) {
                      ref
                          .read(paidTaxInfoProvider.notifier)
                          .getPaidTaxesDetail(shareCode, TaxType.additionalTax);
                    }
                  },
                ),
              )
            ],
          ),
        );
      },
    );
  }

  Widget _buildItemButton(
      {required String title,
      required bool selected,
      required VoidCallback? onPressed}) {
    return GestureDetector(
        onTap: onPressed,
        child: Container(
            height: 24,
            decoration: BoxDecoration(
              color: selected ? Color(0xff488afd) : Color(0xffd3e3fe),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Text(
                title,
                style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: selected ? Colors.white : Colors.black),
                textAlign: TextAlign.center,
              ),
            )));
  }

  /// 纳税数据分析
  Widget _buildEnterpriseMonthPaidTable(TaxInfoInfoState response) {
    return Padding(
        padding: const EdgeInsets.symmetric(
          vertical: 16,
        ),
        child: Column(
          children: [
            SectionLeftMoreHeader(title: '纳税数据分析'),
            SizedBox(height: 10),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Color(0xffE5E7EB),
                  width: 1,
                ),
              ),
              clipBehavior: Clip.antiAlias,
              child: SfDataGrid(
                source: DynamicDataSource(
                  data: response.enterpriseMonthPaidListTableData,
                  columns: response.enterpriseMonthPaidListTableHeadData,
                  cellBuilder: (context, cell, index) {
                    // 在这里处理table body
                    return Container(
                      alignment:
                          index == 0 ? Alignment.centerLeft : Alignment.center,
                      padding: EdgeInsets.symmetric(
                        vertical: 8.0,
                        horizontal: index == 0 ? 8.0 : 0,
                      ),
                      color: index == 0 ? Color(0xFFF8F8F8) : Colors.white,
                      child: Text(
                        cell.value ?? '',
                      ),
                    );
                  },
                ),
                frozenColumnsCount: 1, // 第一列固定
                gridLinesVisibility: GridLinesVisibility.both,
                headerGridLinesVisibility: GridLinesVisibility.both,
                shrinkWrapRows: true, // 自动撑开高度
                showHorizontalScrollbar: false,
                verticalScrollPhysics:
                    NeverScrollableScrollPhysics(), // 关闭滚动，让外层控制
                columns: response.enterpriseMonthPaidListTableHeadData
                    .asMap()
                    .entries
                    .map((entry) {
                  // 在这里处理table head
                  final index = entry.key;
                  final e = entry.value;
                  return GridColumn(
                    columnName: e,
                    width: index == 0 ? 135 : 100, // 第一列宽度不同
                    label: Container(
                      color: Color(0xffF1F5F9),
                      padding: EdgeInsets.symmetric(
                          vertical: 16, horizontal: index == 0 ? 8 : 0),
                      alignment:
                          index == 0 ? Alignment.centerLeft : Alignment.center,
                      child: Text(
                        e,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ));
  }

  /// 滞纳金情况表
  Widget _buildTaxLateFeesListTable(
      List<List<String>> tableData, List<String> header) {
    return Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Column(
          children: [
            SectionHeader(title: '滞纳金情况'),
            SizedBox(height: 10),
            SfDataGrid(
              source: DynamicDataSource(
                data: tableData,
                columns: header,
                cellBuilder: (context, cell, index) {
                  return Container(
                    alignment: Alignment.center,
                    padding: EdgeInsets.symmetric(
                      vertical: 2.0,
                    ),
                    color: Colors.white,
                    child: Text(
                      cell.value ?? '',
                    ),
                  );
                },
              ),
              gridLinesVisibility: GridLinesVisibility.both,
              headerGridLinesVisibility: GridLinesVisibility.both,
              columnWidthMode: ColumnWidthMode.fill,
              shrinkWrapRows: true,
              columns: header.asMap().entries.map((entry) {
                final e = entry.value;
                return GridColumn(
                    columnName: e,
                    label: Container(
                        color: Color(0xffF1F5F9),
                        padding: EdgeInsets.all(16.0),
                        alignment: Alignment.center,
                        child: Text(
                          e,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        )));
              }).toList(),
            ),
            if (tableData.isEmpty)
              Container(
                height: 50,
                decoration: BoxDecoration(
                  border: Border(
                    left: BorderSide(color: Color(0xffE5E7EB), width: 1),
                    right: BorderSide(color: Color(0xffE5E7EB), width: 1),
                    bottom: BorderSide(color: Color(0xffE5E7EB), width: 1),
                  ),
                ),
                child: Center(
                  child: Text("暂无数据"),
                ),
              ),
          ],
        ));
  }

  /// 滞纳金情况表
  Widget _buildTableView(
      String title, List<String> header, List<List<String>> tableData,
      {ColumnWidthMode columnWidthMode = ColumnWidthMode.fill}) {
    return Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Column(
          children: [
            SectionHeader(title: title),
            SizedBox(height: 10),
            SfDataGrid(
              source: DynamicDataSource(
                data: tableData,
                columns: header,
                cellBuilder: (context, cell, index) {
                  // 创建一个GlobalKey来获取RenderBox
                  final textKey = GlobalKey();

                  // 使用Builder来确保能获取到正确的context
                  return Container(
                    alignment: Alignment.center,
                    padding: EdgeInsets.symmetric(vertical: 2.0, horizontal: 4),
                    color: Colors.white,
                    child: StatefulBuilder(
                      builder: (context, _setState) {
                        var textStyle = TextStyle(fontSize: 14);

                        // 使用 WidgetsBinding 添加后置帧回调检查文本是否完整显示
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          final RenderBox? renderBox = textKey.currentContext
                              ?.findRenderObject() as RenderBox?;
                          if (renderBox != null) {
                            final TextPainter painter = TextPainter(
                              text: TextSpan(
                                text: cell.value ?? '',
                                style: textStyle,
                              ),
                              maxLines: 2,
                              textDirection: TextDirection.ltr,
                            )..layout(maxWidth: renderBox.size.width);

                            // 如果文本被截断，可以在这里处理
                            if (painter.didExceedMaxLines) {
                              // 这里需要通过状态管理来更新背景色
                              // 当前的实现方式不太合适，建议使用 StatefulWidget 或状态管理
                              // 暂时移除 print 语句，避免在生产环境中输出日志
                              defaultLogger.debug('didExceedMaxLines');
                            } else {}
                          }
                        });

                        return Container(
                          color: didExceedMaxLines ?? false
                              ? Colors.red
                              : Colors.transparent,
                          child: Text(
                            cell.value ?? '',
                            key: textKey,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: textStyle,
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
              gridLinesVisibility: GridLinesVisibility.both,
              headerGridLinesVisibility: GridLinesVisibility.both,
              columnWidthMode: columnWidthMode,
              verticalScrollPhysics: NeverScrollableScrollPhysics(),
              shrinkWrapRows: true,
              columns: header.asMap().entries.map((entry) {
                final e = entry.value;
                return GridColumn(
                    columnName: e,
                    label: Container(
                        color: Color(0xffF1F5F9),
                        padding: EdgeInsets.all(16.0),
                        alignment: Alignment.center,
                        child: Text(
                          e,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        )));
              }).toList(),
              // onQueryRowHeight: (details) {
              //   if (details.rowIndex > 0) {
              //     final textPainter = TextPainter(
              //       text: TextSpan(
              //         text: tableData.first[3],
              //         style: TextStyle(fontSize: 14),
              //       ),
              //       maxLines: null,
              //       textDirection: TextDirection.ltr,
              //     );
              //     textPainter.layout(maxWidth: 100);
              //     return textPainter.height + 16;
              //   }
              //   return details.rowHeight;
              // },
            ),
            if (tableData.isEmpty)
              Container(
                height: 50,
                decoration: BoxDecoration(
                  border: Border(
                    left: BorderSide(color: Color(0xffE5E7EB), width: 1),
                    right: BorderSide(color: Color(0xffE5E7EB), width: 1),
                    bottom: BorderSide(color: Color(0xffE5E7EB), width: 1),
                  ),
                ),
                child: Center(
                  child: Text("暂无数据"),
                ),
              ),
          ],
        ));
  }

  // /// 近两年纳税信用评级表
  // Widget _buildCreditEvaluationListTable(TaxInfoInfoState response) {
  //   return Padding(
  //       padding: const EdgeInsets.symmetric(vertical: 16),
  //       child: Column(
  //         children: [
  //           SectionHeader(title: '近两年纳税信用评级'),
  //           SizedBox(height: 10),
  //           SfDataGrid(
  //             source: DynamicDataSource(
  //               data: response.creditEvaluationListTableData(),
  //               columns: response.creditEvaluationListTableHeadData,
  //               cellBuilder: (context, cell, index) {
  //                 return Container(
  //                   alignment: Alignment.center,
  //                   padding: EdgeInsets.symmetric(
  //                     vertical: 2.0,
  //                   ),
  //                   color: Colors.white,
  //                   child: Text(
  //                     cell.value ?? '',
  //                   ),
  //                 );
  //               },
  //             ),
  //             gridLinesVisibility: GridLinesVisibility.both,
  //             headerGridLinesVisibility: GridLinesVisibility.both,
  //             columnWidthMode: ColumnWidthMode.auto,
  //             shrinkWrapRows: true,
  //             verticalScrollPhysics: NeverScrollableScrollPhysics(),
  //             columns: response.creditEvaluationListTableHeadData
  //                 .asMap()
  //                 .entries
  //                 .map((entry) {
  //               final e = entry.value;
  //               return GridColumn(
  //                   columnName: e,
  //                   label: Container(
  //                       color: Color(0xffF1F5F9),
  //                       padding: EdgeInsets.all(16.0),
  //                       alignment: Alignment.center,
  //                       child: Text(
  //                         e,
  //                         style: TextStyle(
  //                           fontSize: 14,
  //                           fontWeight: FontWeight.w600,
  //                           color: Colors.black,
  //                         ),
  //                       )));
  //             }).toList(),
  //             columnSizer: CustomGridColumnSizer(),
  //             onQueryRowHeight: (details) {
  //               if (details.rowIndex > 0) {
  //                 final textPainter = TextPainter(
  //                   text: TextSpan(
  //                     text: response.creditEvaluationListTableData().first[3],
  //                     style: TextStyle(fontSize: 14),
  //                   ),
  //                   maxLines: null,
  //                   textDirection: TextDirection.ltr,
  //                 );
  //                 textPainter.layout(maxWidth: 100);
  //                 return textPainter.height + 16;
  //               }
  //               return details.rowHeight;
  //             },
  //           )
  //         ],
  //       ));
  // }

  /// 财务情况表
  Widget _buildSpiderFinanceInfoTable(TaxInfoInfoState response) {
    return Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Column(
          children: [
            SectionLeftMoreHeader(title: '财务情况(元)'),
            SizedBox(height: 10),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Color.fromRGBO(229, 231, 235, 1),
                  width: 1,
                ),
              ),
              clipBehavior: Clip.antiAlias,
              child: SfDataGrid(
                source: DynamicDataSource(
                  data: response.spiderFinanceInfosYearListTableData(),
                  columns: response.spiderFinanceInfosYearListTableHeadData,
                  cellBuilder: (context, cell, index) {
                    // 在这里处理table body
                    return Container(
                      alignment:
                          index == 0 ? Alignment.centerLeft : Alignment.center,
                      padding: EdgeInsets.symmetric(
                        vertical: 8.0,
                      ),
                      color: index == 0 ? Color(0xFFF8F8F8) : Colors.white,
                      child: Text(
                        cell.value ?? '',
                      ),
                    );
                  },
                ),
                frozenColumnsCount: 1, // 第一列固定
                gridLinesVisibility: GridLinesVisibility.both,
                headerGridLinesVisibility: GridLinesVisibility.both,
                shrinkWrapRows: true, // 自动撑开高度
                showHorizontalScrollbar: false,
                verticalScrollPhysics:
                    NeverScrollableScrollPhysics(), // 关闭滚动，让外层控制
                columns: response.spiderFinanceInfosYearListTableHeadData
                    .asMap()
                    .entries
                    .map((entry) {
                  // 在这里处理table head
                  final index = entry.key;
                  final e = entry.value;
                  return GridColumn(
                    columnName: e,
                    width: index == 0 ? 140 : 100, // 第一列宽度不同
                    label: Container(
                      color: Color(0xffF1F5F9),
                      padding: EdgeInsets.symmetric(
                          vertical: 16, horizontal: index == 0 ? 8 : 0),
                      alignment:
                          index == 0 ? Alignment.centerLeft : Alignment.center,
                      child: Text(
                        e,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            )
          ],
        ));
  }
}

class CustomGridColumnSizer extends ColumnSizer {
  @override
  double computeHeaderCellWidth(GridColumn column, TextStyle style) {
    return super.computeHeaderCellWidth(column, style);
  }

  @override
  double computeCellWidth(GridColumn column, DataGridRow row, Object? cellValue,
      TextStyle textStyle) {
    return super.computeCellWidth(column, row, cellValue, textStyle);
  }

  @override
  double computeHeaderCellHeight(GridColumn column, TextStyle textStyle) {
    return super.computeHeaderCellHeight(column, textStyle);
  }

  @override
  double computeCellHeight(GridColumn column, DataGridRow row,
      Object? cellValue, TextStyle textStyle) {
    return super.computeCellHeight(column, row, cellValue, textStyle);
  }
}
