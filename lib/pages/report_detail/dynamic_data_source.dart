import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

class DynamicDataSource extends DataGridSource {
  final List<List<String>> data;
  final List<String> columns;
  final Widget Function(BuildContext context, DataGridCell cell, int index)
      cellBuilder;

  DynamicDataSource({
    required this.data,
    required this.columns,
    required this.cellBuilder,
  }) {
    _rows = data.map<DataGridRow>((row) {
      return DataGridRow(
        cells: List.generate(
          columns.length,
          (index) => DataGridCell<String>(
            columnName: columns[index],
            value: index < row.length ? row[index] : '',
          ),
        ),
      );
    }).toList();
  }

  late List<DataGridRow> _rows;

  @override
  List<DataGridRow> get rows => _rows;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    return DataGridRowAdapter(
      cells: row.getCells().asMap().entries.map<Widget>((entry) {
        final index = entry.key;
        final cell = entry.value;

        return Builder(
          builder: (context) => cellBuilder(context, cell, index),
        );
      }).toList(),
    );
  }
}
