import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/report_detail/dynamic_data_source.dart';
import 'package:zrreport/pages/report_detail/paidTaxes/paid_taxes_provider.dart';
import 'package:zrreport/pages/report_detail/paidTaxes/taxes_provider.dart';
import 'package:zrreport/pages/report_detail/widget/section_header.dart';
import 'package:zrreport/pages/report_detail/widget/section_left_more_header.dart';

class TaxInfoView extends BasePage {
  final String shareCode;
  final bool isExample;
  const TaxInfoView({super.key, required this.shareCode, this.isExample = false});

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ProviderScope.containerOf(context)
          .read(taxInfoProvider.notifier)
          .getTaxesDetail(shareCode, isExample: isExample);
    });

    return Padding(
      padding: EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          children: [
            Consumer(
              builder: (context, ref, child) {
                final response = ref.watch(taxInfoProvider);
                switch (response.pageStatus) {
                  case PageStatus.initial:
                  case PageStatus.loading:
                    return Center(
                      child: LoadingWidget(),
                    );
                  case PageStatus.success:
                    return _buildContentView();
                  case PageStatus.empty:
                    return Container(
                      child: Text(''),
                    );
                  case PageStatus.error:
                    return Container(
                      child: Text('error'),
                    );
                }
              },
            )
          ],
        ),
      ),
    );
  }

  Widget _buildContentView() {
    return Consumer(builder: (context, ref, child) {
      final response = ref.read(taxInfoProvider);
      return Column(
        children: [
          // header list
          ...response.taxInfoHeadList.map((e) {
            return AdaptiveTextRow(
                label: e.title, value: e.value, isOdd: e.dark);
          }),

          SizedBox(height: 24),

          _buildLastThreeTaxesTable(),

          response.hasEnterpriseMonthPaidList
              ? // 纳税数据分析
              _buildEnterpriseMonthPaidTable(response)
              : Container(),

          /// 滞纳金情况表
          response.hasTaxLateFeesList
              ? _buildTaxLateFeesListTable(response)
              : Container(),

          /// 近两年纳税信用评级表
          response.hasCreditEvaluationList
              ? _buildCreditEvaluationListTable(response)
              : Container(),

          /// 税务违法违规信息
          response.hasLawRegulationViolationVoList
              ? // 税务违法违规信息
              _buildLawRegulationViolationVoListTable(response, context)
              : Container(),

          /// 财务情况
          response.hasSpiderFinanceInfos
              ? // 财务情况
              _buildSpiderFinanceInfoTable(response)
              : Container(),
        ],
      );
    });
  }

  /// 近三年纳税信息
  Widget _buildLastThreeTaxesTable() {
    return Consumer(builder: (context, ref, child) {
      final response = ref.watch(paidTaxInfoProvider);
      if (!response.hasPaidTaxInfo) {
        ref
            .read(paidTaxInfoProvider.notifier)
            .getPaidTaxesDetail(shareCode, response.taxType, isExample: isExample);
      }
      return Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 16,
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(child: SectionHeader(title: '近三年纳税信息')),
                  switchWidget(),
                  SizedBox(width: 15)
                ],
              ),
              SizedBox(height: 12),
              Text(
                response.taxTipContent(),
                style: TextStyle(
                  fontSize: 13,
                  color: Color(0xff999999),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 12),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Color(0xffE5E7EB),
                    width: 1,
                  ),
                ),
                clipBehavior: Clip.antiAlias,
                child: SfDataGrid(
                  source: DynamicDataSource(
                    data: response.padiTaxesListTableData,
                    columns: response.padiTaxesListTableHeadData,
                    cellBuilder: (context, cell, index) {
                      // 在这里处理table body
                      return Container(
                        alignment: Alignment.center,
                        padding: EdgeInsets.symmetric(
                          vertical: 8.0,
                          horizontal: index == 0 ? 8.0 : 0,
                        ),
                        color: Colors.white,
                        child: Text(
                          cell.value ?? '',
                        ),
                      );
                    },
                  ),
                  // frozenColumnsCount: 1, // 第一列固定
                  gridLinesVisibility: GridLinesVisibility.both,
                  headerGridLinesVisibility: GridLinesVisibility.both,
                  showHorizontalScrollbar: false,
                  shrinkWrapRows: true, // 自动撑开高度
                  // shrinkWrapColumns: true,
                  verticalScrollPhysics:
                      NeverScrollableScrollPhysics(), // 关闭滚动，让外层控制
                  columns: response.padiTaxesListTableHeadData
                      .asMap()
                      .entries
                      .map((entry) {
                    // 在这里处理table head
                    final index = entry.key;
                    final e = entry.value;
                    return GridColumn(
                      columnName: e,
                      width: index == 0
                          ? 60
                          : (MediaQuery.of(context).size.width - 60 - 30) /
                              3, // 第一列宽度不同
                      label: Container(
                        color: Color(0xffF1F5F9),
                        padding: EdgeInsets.symmetric(
                            vertical: 16, horizontal: index == 0 ? 8 : 0),
                        alignment: Alignment.center,
                        child: Text(
                          e,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              )
            ],
          ));
    });
  }

  Widget switchWidget() {
    return Consumer(
      builder: (context, ref, child) {
        final response = ref.watch(paidTaxInfoProvider);
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: Color(0xffd3e3fe),
          ),
          height: 24,
          width: 110,
          child: Row(
            children: [
              Expanded(
                child: _buildItemButton(
                  title: '正税',
                  selected: response.taxType == TaxType.normalTax,
                  onPressed: () {
                    if (response.taxType != TaxType.normalTax) {
                      ref
                          .read(paidTaxInfoProvider.notifier)
                          .getPaidTaxesDetail(shareCode, TaxType.normalTax);
                    }
                  },
                ),
              ),
              Expanded(
                child: _buildItemButton(
                  title: '附加税',
                  selected: response.taxType == TaxType.additionalTax,
                  onPressed: () {
                    if (response.taxType != TaxType.additionalTax) {
                      ref
                          .read(paidTaxInfoProvider.notifier)
                          .getPaidTaxesDetail(shareCode, TaxType.additionalTax);
                    }
                  },
                ),
              )
            ],
          ),
        );
      },
    );
  }

  Widget _buildItemButton(
      {required String title,
      required bool selected,
      required VoidCallback? onPressed}) {
    return GestureDetector(
        onTap: onPressed,
        child: Container(
            height: 24,
            decoration: BoxDecoration(
              color: selected ? Color(0xff488afd) : Color(0xffd3e3fe),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Text(
                title,
                style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: selected ? Colors.white : Colors.black),
                textAlign: TextAlign.center,
              ),
            )));
  }

  /// 纳税数据分析
  Widget _buildEnterpriseMonthPaidTable(TaxInfoInfoState response) {
    return Padding(
        padding: const EdgeInsets.symmetric(
          vertical: 16,
        ),
        child: Column(
          children: [
            SectionLeftMoreHeader(title: '纳税数据分析'),
            SizedBox(height: 10),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Color(0xffE5E7EB),
                  width: 1,
                ),
              ),
              clipBehavior: Clip.antiAlias,
              child: SfDataGrid(
                source: DynamicDataSource(
                  data: response.enterpriseMonthPaidListTableData,
                  columns: response.enterpriseMonthPaidListTableHeadData,
                  cellBuilder: (context, cell, index) {
                    // 在这里处理table body
                    return Container(
                      alignment:
                          index == 0 ? Alignment.centerLeft : Alignment.center,
                      padding: EdgeInsets.symmetric(
                        vertical: 8.0,
                        horizontal: index == 0 ? 8.0 : 0,
                      ),
                      color: index == 0 ? Color(0xFFF8F8F8) : Colors.white,
                      child: Text(
                        cell.value ?? '',
                      ),
                    );
                  },
                ),
                frozenColumnsCount: 1, // 第一列固定
                gridLinesVisibility: GridLinesVisibility.both,
                headerGridLinesVisibility: GridLinesVisibility.both,
                shrinkWrapRows: true, // 自动撑开高度
                showHorizontalScrollbar: false,
                verticalScrollPhysics:
                    NeverScrollableScrollPhysics(), // 关闭滚动，让外层控制
                columns: response.enterpriseMonthPaidListTableHeadData
                    .asMap()
                    .entries
                    .map((entry) {
                  // 在这里处理table head
                  final index = entry.key;
                  final e = entry.value;
                  return GridColumn(
                    columnName: e,
                    width: index == 0 ? 135 : 100, // 第一列宽度不同
                    label: Container(
                      color: Color(0xffF1F5F9),
                      padding: EdgeInsets.symmetric(
                          vertical: 16, horizontal: index == 0 ? 8 : 0),
                      alignment:
                          index == 0 ? Alignment.centerLeft : Alignment.center,
                      child: Text(
                        e,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            )
          ],
        ));
  }

  /// 滞纳金情况表
  Widget _buildTaxLateFeesListTable(TaxInfoInfoState response) {
    return Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Column(
          children: [
            SectionHeader(title: '近两年纳税信用评级'),
            SizedBox(height: 10),
            Container(
                child: SfDataGrid(
              source: DynamicDataSource(
                data: response.taxLateFeesListTableData(),
                columns: response.taxLateFeesListTableHeadData,
                cellBuilder: (context, cell, index) {
                  return Container(
                    alignment: Alignment.center,
                    padding: EdgeInsets.symmetric(
                      vertical: 8.0,
                    ),
                    color: Colors.white,
                    child: Text(
                      cell.value ?? '',
                    ),
                  );
                },
              ),
              gridLinesVisibility: GridLinesVisibility.both,
              headerGridLinesVisibility: GridLinesVisibility.both,
              columnWidthMode: ColumnWidthMode.fill,
              columns: response.taxLateFeesListTableHeadData
                  .asMap()
                  .entries
                  .map((entry) {
                final e = entry.value;
                return GridColumn(
                    columnName: e,
                    label: Container(
                        color: Color(0xffF1F5F9),
                        padding: EdgeInsets.all(16.0),
                        alignment: Alignment.center,
                        child: Text(
                          e,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        )));
              }).toList(),
            ))
          ],
        ));
  }

  /// 近两年纳税信用评级表
  Widget _buildCreditEvaluationListTable(TaxInfoInfoState response) {
    return Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Column(
          children: [
            SectionHeader(title: '近两年纳税信用评级'),
            SizedBox(height: 10),
            SfDataGrid(
              source: DynamicDataSource(
                data: response.creditEvaluationListTableData(),
                columns: response.creditEvaluationListTableHeadData,
                cellBuilder: (context, cell, index) {
                  return Container(
                    alignment: Alignment.center,
                    padding: EdgeInsets.symmetric(
                      vertical: 2.0,
                    ),
                    color: Colors.white,
                    child: Text(
                      cell.value ?? '',
                    ),
                  );
                },
              ),
              gridLinesVisibility: GridLinesVisibility.both,
              headerGridLinesVisibility: GridLinesVisibility.both,
              columnWidthMode: ColumnWidthMode.fitByCellValue,
              shrinkWrapRows: true,
              columns: response.creditEvaluationListTableHeadData
                  .asMap()
                  .entries
                  .map((entry) {
                final e = entry.value;
                return GridColumn(
                    columnName: e,
                    label: Container(
                        color: Color(0xffF1F5F9),
                        padding: EdgeInsets.all(16.0),
                        alignment: Alignment.center,
                        child: Text(
                          e,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        )));
              }).toList(),
              onQueryRowHeight: (details) {
                if (details.rowIndex > 0) {
                   final textPainter = TextPainter(
                      text: TextSpan(
                        text: response.creditEvaluationListTableData().first[2],
                        style: TextStyle(fontSize: 14),
                      ),
                      maxLines: null,
                      textDirection: TextDirection.ltr,
                    );
                    textPainter.layout(maxWidth: 100); 
              return textPainter.height + 16;
                }
                return details.rowHeight;
              },
            )
          ],
        ));
  }

  /// 税务违法违规表
  Widget _buildLawRegulationViolationVoListTable(TaxInfoInfoState response, BuildContext context) {
    return Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Column(
          children: [
            SectionLeftMoreHeader(title: '税务违法违规信息'),
            SizedBox(height: 10),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Color(0xffE5E7EB),
                  width: 1,
                ),
              ),
              clipBehavior: Clip.antiAlias,
              child: SfDataGrid(
                source: DynamicDataSource(
                  data: response.lawRegulationViolationVoListTableData(),
                  columns: response.lawRegulationViolationVoListTableHeadData,
                  cellBuilder: (context, cell, index) {
                    // 在这里处理table body
                    return Container(
                      alignment:
                          index == 0 ? Alignment.centerLeft : Alignment.center,
                      padding: EdgeInsets.symmetric(
                        vertical: 8.0,
                      ),
                      color: index == 0 ? Color(0xFFF8F8F8) : Colors.white,
                      child: Text(
                        cell.value ?? '',
                      ),
                    );
                  },
                ),
                frozenColumnsCount: 1, // 第一列固定
                gridLinesVisibility: GridLinesVisibility.both,
                headerGridLinesVisibility: GridLinesVisibility.both,
                shrinkWrapRows: true, // 自动撑开高度
                shrinkWrapColumns: true,
                verticalScrollPhysics:
                    NeverScrollableScrollPhysics(), // 关闭滚动，让外层控制
                columns: response.lawRegulationViolationVoListTableHeadData
                    .asMap()
                    .entries
                    .map((entry) {
                  // 在这里处理table head
                  final index = entry.key;
                  final e = entry.value;
                  return GridColumn(
                    columnName: e,
                    width: index == 0 ? 135 : (MediaQuery.of(context).size.width - 130 - 30) / 2, 
                    label: Container(
                      color: Color(0xffF1F5F9),
                      padding: EdgeInsets.symmetric(
                          vertical: 16, horizontal: index == 0 ? 8 : 0),
                      alignment:
                          index == 0 ? Alignment.centerLeft : Alignment.center,
                      child: Text(
                        e,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            )
          ],
        ));
  }

  /// 财务情况表
  Widget _buildSpiderFinanceInfoTable(TaxInfoInfoState response) {
    return Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Column(
          children: [
            SectionLeftMoreHeader(title: '财务情况(元)'),
            SizedBox(height: 10),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Color.fromRGBO(229, 231, 235, 1),
                  width: 1,
                ),
              ),
              clipBehavior: Clip.antiAlias,
              child: SfDataGrid(
                source: DynamicDataSource(
                  data: response.spiderFinanceInfosYearListTableData(),
                  columns: response.spiderFinanceInfosYearListTableHeadData,
                  cellBuilder: (context, cell, index) {
                    // 在这里处理table body
                    return Container(
                      alignment:
                          index == 0 ? Alignment.centerLeft : Alignment.center,
                      padding: EdgeInsets.symmetric(
                        vertical: 8.0,
                      ),
                      color: index == 0 ? Color(0xFFF8F8F8) : Colors.white,
                      child: Text(
                        cell.value ?? '',
                      ),
                    );
                  },
                ),
                frozenColumnsCount: 1, // 第一列固定
                gridLinesVisibility: GridLinesVisibility.both,
                headerGridLinesVisibility: GridLinesVisibility.both,
                shrinkWrapRows: true, // 自动撑开高度
                showHorizontalScrollbar: false,
                verticalScrollPhysics:
                    NeverScrollableScrollPhysics(), // 关闭滚动，让外层控制
                columns: response.spiderFinanceInfosYearListTableHeadData
                    .asMap()
                    .entries
                    .map((entry) {
                  // 在这里处理table head
                  final index = entry.key;
                  final e = entry.value;
                  return GridColumn(
                    columnName: e,
                    width: index == 0 ? 140 : 100, // 第一列宽度不同
                    label: Container(
                      color: Color(0xffF1F5F9),
                      padding: EdgeInsets.symmetric(
                          vertical: 16, horizontal: index == 0 ? 8 : 0),
                      alignment:
                          index == 0 ? Alignment.centerLeft : Alignment.center,
                      child: Text(
                        e,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            )
          ],
        ));
  }
}
