import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:zrreport/common/index.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:zrreport/pages/report_detail/dynamic_data_source.dart';
import 'package:zrreport/pages/report_detail/supplier_information/supplier_information_provider.dart';
import 'package:zrreport/pages/report_detail/widget/section_header.dart';

class SupplierInformationView extends StatelessWidget {
  const SupplierInformationView({super.key, required this.shareCode, this.isExample = false});

  final String shareCode;
  final bool isExample;

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ProviderScope.containerOf(context)
          .read(supplierInfoProvider.notifier)
          .loadData(shareCode, isExample);
    });

    return Consumer(
      builder: (context, ref, child) {
        final response = ref.watch(supplierInfoProvider);
        switch (response.pageStatus) {
          case PageStatus.initial:
          case PageStatus.loading:
            return Center(
              child: LoadingWidget(),
            );
          case PageStatus.success:
            return SupplierInformationBodyView(shareCode: shareCode, isExample: isExample);
          case PageStatus.empty:
            return Center(
              child: Text(''),
            );
          case PageStatus.error:
            return Center(
              child: Text(''),
            );
        }
      },
    );
  }
}

class SupplierInformationBodyView extends HookWidget {
  final String shareCode;
  final bool isExample;
  const SupplierInformationBodyView({super.key, required this.shareCode, this.isExample = false});


  @override
  Widget build(BuildContext context) {
    final scrollController = useScrollController();
    return Consumer(builder: (context, ref, child) {
      final notifier = ref.read(supplierInfoShowButtonProvider.notifier);
      scrollController.addListener(() {
        if (scrollController.offset > 0) {
          notifier.show();
        } else {
          notifier.hidden();
        }
      });
      final response = ref.read(supplierInfoProvider);
      return Stack(
        children: [
          SingleChildScrollView(
            controller: scrollController,
            padding: EdgeInsets.all(16),
            child: Column(
              children: [
                SectionHeader(title: '前十供应商信息（近三年）'),
                SizedBox(height: 16),
                _buildTable1(response),
                SizedBox(height: 16),
                SectionHeader(title: '前十销售客户信息（近三年）'),
                SizedBox(height: 16),
                _buildTable2(response),
                response.table2HasData ? Container() : emptyWidget(),
              ],
            ),
          ),
          Positioned(
            right: 32,
            bottom: 32,
            child: Consumer(builder: (context, ref, child) {
              return Visibility(
                visible: ref.watch(supplierInfoShowButtonProvider),
                child: InkWell(
                  onTap: () {
                    scrollController.animateTo(
                      0.0, // 滚动到顶部位置 (0.0)
                      duration: const Duration(milliseconds: 500), // 滚动动画时长
                      curve: Curves.easeOut, // 滚动动画曲线
                    );
                  },
                  child: Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      color: Color(0xFFDDDDDD),
                      borderRadius: BorderRadius.circular(22),
                    ),
                    child: Center(
                      child: Icon(Icons.arrow_upward),
                    ),
                  ),
                ),
              );
            }),
          ),
        ],
      );
    });
  }

  /// 前十供应商信息（近三年）表格
  Widget _buildTable1(SupplierInformationViewModel response) {
    return SfDataGrid(
      source: DynamicDataSource(
        data: response.tableData1,
        columns: response.tableHeadData1,
        cellBuilder: (context, cell, index) {
          // 在这里处理table body
          return Container(
            alignment: Alignment.center,
            padding: EdgeInsets.all(8.0),
            child: Text(cell.value ?? ''),
          );
        },
      ),
      columnWidthMode: ColumnWidthMode.fill,
      rowHeight: 100,
      gridLinesVisibility: GridLinesVisibility.both,
      headerGridLinesVisibility: GridLinesVisibility.both,
      shrinkWrapRows: true, // 自动撑开高度
      verticalScrollPhysics: NeverScrollableScrollPhysics(), // 关闭滚动，让外层控制
      columns: response.tableHeadData1.map((e) {
        // 在这里处理table head
        return GridColumn(
          columnName: e,
          label: Container(
            color: Color(0xffF1F5F9),
            alignment: Alignment.center,
            child: Text(
              e,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// 前十供应商信息（近三年）表格
  Widget _buildTable2(SupplierInformationViewModel response) {
    return SfDataGrid(
      source: DynamicDataSource(
        data: response.tableData2,
        columns: response.tableHeadData1,
        cellBuilder: (context, cell, index) {
          // 在这里处理table body
          return Container(
            alignment: Alignment.center,
            padding: EdgeInsets.all(8.0),
            child: Text(cell.value ?? ''),
          );
        },
      ),
      columnWidthMode: ColumnWidthMode.fill,
      rowHeight: 100,
      gridLinesVisibility: GridLinesVisibility.both,
      headerGridLinesVisibility: GridLinesVisibility.both,
      shrinkWrapRows: true, // 自动撑开高度
      verticalScrollPhysics: NeverScrollableScrollPhysics(), // 关闭滚动，让外层控制
      columns: response.tableHeadData1.map((e) {
        // 在这里处理table head
        return GridColumn(
          columnName: e,
          label: Container(
            color: Color(0xffF1F5F9),
            alignment: Alignment.center,
            child: Text(
              e,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget emptyWidget() {
    return Container(
      height: 50,
      decoration: BoxDecoration(
        border: Border(
          left: BorderSide(color: Color(0xffE5E7EB), width: 1),
          right: BorderSide(color: Color(0xffE5E7EB), width: 1),
          bottom: BorderSide(color: Color(0xffE5E7EB), width: 1),
        ),
      ),
      child: Center(
        child: Text(
          '暂无数据',
          style: TextStyle(color: Colors.grey),
        ),
      ),
    );
  }
}
