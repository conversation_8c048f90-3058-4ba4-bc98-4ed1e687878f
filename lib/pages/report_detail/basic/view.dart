import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:zrreport/common/index.dart';
import 'package:zrreport/pages/report_detail/basic/basic_provider.dart';
import 'package:zrreport/pages/report_detail/dynamic_data_source.dart';

class ReportBasicInfoView extends BasePage {
  final String shareCode;
  final bool isExample;
  const ReportBasicInfoView({super.key, required this.shareCode, this.isExample = false});

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ProviderScope.containerOf(context)
          .read(basicInfoProvider.notifier)
          .getReposrtDetail(shareCode, isExample: isExample);
    });

    return Consumer(
      builder: (context, ref, child) {
         final detailProvider = ref.watch(basicInfoProvider);
        return detailProvider.model != null
            ? Container(
                child: _contentView(detailProvider.model!),
              )
            : LoadingWidget();
      },
    );
  }

  Widget _contentView(ReportDetailModel model) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 基本信息
          _buildBasicInfoSection(model),

          SizedBox(height: 15),

          // 变更信息
          _buildChangeInfoSection(),

          SizedBox(height: 15),

          // 银税互动授权记录
          _buildAuthRecordSection(model),

          SizedBox(height: 30),

          // 股东明细
          model.qccEnterprisePartnersList.isNotEmpty
              ? _buildPartnerSection()
              : SizedBox(),

          // 经营范围
          if (model.businessScope.isNotEmpty) 
             _buildbusinessScopeSection(model),
          

          SizedBox(height: 50),
        ],
      ),
    );
  }

  Widget _buildBasicInfoSection(ReportDetailModel model) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AdaptiveTextRow(label: '企业名称', value: model.enterpriseName, canCopy: true, isOdd: true),
        AdaptiveTextRow(label: '当前法人姓名', value: model.legalPerson, isOdd: false),
        AdaptiveTextRow(label: '法人年龄(岁)', value: model.age.toString(), isOdd: true),
        AdaptiveTextRow(label: '法人是否占股', value: model.stockStatus ? '是' : '否', isOdd: false),
        AdaptiveTextRow(label: '法人股份占比(%)', value: model.stockPercent?.toStringAsFixed(2) ?? '-'),
        AdaptiveTextRow(label: '成立日期', value: model.registerDate, isOdd: false),
        AdaptiveTextRow(label: '成立时长', value: model.registerMonth.toString()),
        AdaptiveTextRow(label: '统一信用代码', value: model.creditCode, isOdd: false, canCopy: true),
        AdaptiveTextRow(label: '行业分类', value: model.industry),
        AdaptiveTextRow(label: '注册类型', value: model.loginRegisterType, isOdd: false),
        AdaptiveTextRow(label: '注册区域', value: model.registerAddress),
        model.enterpriseChange == null
            ? SizedBox()
            : AdaptiveTextRow(label:  '${model.enterpriseChange!.name}', 
            value: '变更前:${model.enterpriseChange?.beforeContent}\n\n变更后:${model.enterpriseChange?.affterContent}',
            isOdd: false, maxLines: 4)
      ],
    );
  }

  Widget _buildChangeInfoSection() {
    return Consumer(builder: (context, ref, child) {
      final state = ref.watch(basicInfoProvider);
      return 
         SfDataGrid(
              source: DynamicDataSource(
                data: state.enterpriseChangeListTableData,
                columns: state.enterpriseChangeListTableHeadData,
                cellBuilder: (context, cell, index) {
                  return Container(
                    alignment: Alignment.center,
                    color: Colors.white,
                    child: Text(
                      cell.value ?? '',
                    ),
                  );
                },
              ),
              gridLinesVisibility: GridLinesVisibility.both,
              headerGridLinesVisibility: GridLinesVisibility.both,
              columnWidthMode: ColumnWidthMode.fill,
              shrinkWrapRows: true,
              rowHeight: 120,
              columns: state.enterpriseChangeListTableHeadData
                  .asMap()
                  .entries
                  .map((entry) {
                final e = entry.value;
                return GridColumn(
                    columnName: e,
                    label: Container(
                        color: Color(0xffF1F5F9),
                        padding: EdgeInsets.all(16.0),
                        alignment: Alignment.center,
                        child: Text(
                          e,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        )));
              }).toList(),
            );
    });
  }

  Widget _buildAuthRecordSection(ReportDetailModel model) {
    return Container(
      color: Color(0xffF1F5F9),
      height: 44,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(width: 15),
          Text('银税互动授权记录',
              style: TextStyle(fontSize: 13, color: Color(0xffA8A8A8))),
          Expanded(
              child: Text(model.bankTaxRecord,
                  style: TextStyle(
                      fontSize: 13,
                      color: Color(0xff222222),
                      fontWeight: FontWeight.w600),
                  textAlign: TextAlign.right)),
          SizedBox(width: 10),
        ],
      ),
    );
  }

  Widget _buildbusinessScopeSection(ReportDetailModel model) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('经营范围'),
        const SizedBox(height: 15),
        Container(
            color: Color(0xffF1F5F9),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
              child: Text(
                model.businessScope,
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xff111111),
                ),
              ),
            ))
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Container(
          width: 5,
          height: 16,
          decoration: BoxDecoration(
              color: Color(0xff488AFD),
              borderRadius: BorderRadius.vertical(
                  top: Radius.circular(2.5), bottom: Radius.circular(2.5))),
        ),
        SizedBox(width: 9),
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildPartnerSection() {
    return Consumer(builder: (context, ref, child) {
      final state = ref.watch(basicInfoProvider);
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('股东明细'),
          const SizedBox(height: 15),
            SfDataGrid(
              source: DynamicDataSource(
                data: state.enterprisePartnersListTableData,
                columns: state.enterprisePartnersListTableHeadData,
                cellBuilder: (context, cell, index) {
                  return Container(
                    alignment: Alignment.center,
                    color: Colors.white,
                    child: Text(
                      cell.value ?? '',
                    ),
                  );
                },
              ),
              gridLinesVisibility: GridLinesVisibility.both,
              headerGridLinesVisibility: GridLinesVisibility.both,
              columnWidthMode: ColumnWidthMode.fill,
              shrinkWrapRows: true,
              columns: state.enterprisePartnersListTableHeadData
                  .asMap()
                  .entries
                  .map((entry) {
                final e = entry.value;
                return GridColumn(
                    columnName: e,
                    label: Container(
                        color: Color(0xffF1F5F9),
                        padding: EdgeInsets.all(16.0),
                        alignment: Alignment.center,
                        child: Text(
                          e,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        )));
              }).toList(),
            )
        ],
      );
    });
  }
}
